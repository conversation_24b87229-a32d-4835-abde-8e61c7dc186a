<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offerte Generator Pro</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    
    <style>
        /* --- THEME KLEUREN --- */
        :root {
            --primary-color: #16a085;
            --primary-hover: #1abc9c;
            --dark-color: #2c3e50;
            --light-gray: #ecf0f1;
            --text-color: #34495e;
            --border-color: #bdc3c7;
            --form-bg: #ffffff;
            --app-bg: #f0f2f5;
            --danger-color: #e74c3c;
            --shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        /* --- ALGEMENE STIJL APPLICATIE --- */
        *, *::before, *::after { box-sizing: border-box; }

        body {
            font-family: 'Open Sans', sans-serif;
            background-color: var(--app-bg);
            margin: 0;
            color: var(--text-color);
        }
        
        .app-header {
            background: var(--dark-color);
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 100;
        }
        .app-header h1 { font-family: 'Montserrat', sans-serif; margin: 0; font-size: 1.5rem; }
        .app-header .actions button {
            background-color: var(--primary-color); color: white; border: none; padding: 10px 20px;
            border-radius: 5px; cursor: pointer; font-weight: 600;
            transition: background-color 0.2s, transform 0.2s; margin-left: 10px;
        }
        .app-header .actions button:hover { background-color: var(--primary-hover); transform: translateY(-2px); }
        .app-header .actions button#settings-btn { background-color: #3498db; }
        .app-header .actions button#settings-btn:hover { background-color: #5dade2; }

        .main-container { display: grid; grid-template-columns: 480px 1fr; gap: 25px; padding: 25px; }

        .form-container, .preview-container {
            border-radius: 8px; background: var(--form-bg); box-shadow: var(--shadow);
            overflow-y: auto; max-height: calc(100vh - 120px);
        }
        
        /* --- FORMULIER STIJL --- */
        .form-container { padding: 15px 10px 15px 25px; }
        .form-section { margin-bottom: 30px; border-left: 3px solid var(--light-gray); padding-left: 20px; }
        .form-section h2 {
            font-family: 'Montserrat', sans-serif; font-size: 1.1rem; color: var(--dark-color);
            margin: 0 0 20px 0; display: flex; align-items: center; gap: 10px;
        }
        .form-section h2 svg { width: 20px; height: 20px; fill: var(--primary-color); }
        .form-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
        .form-group { display: flex; flex-direction: column; gap: 5px; }
        label { font-size: 0.85rem; font-weight: 600; color: var(--text-color); }
        input[type="text"], input[type="date"], input[type="email"], input[type="number"], textarea {
            width: 100%; padding: 10px 12px; border: 1px solid #ccc; border-radius: 4px;
            font-size: 0.95rem; font-family: 'Open Sans', sans-serif;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        input:focus, textarea:focus {
            outline: none; border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.2);
        }
        textarea { resize: vertical; min-height: 80px; }
        .full-width { grid-column: 1 / -1; }

        #items-form-container .item-row { background-color: #fdfdfd; border: 1px solid var(--light-gray); border-radius: 6px; padding: 15px; margin-bottom: 15px; }
        .item-row-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .item-row-header strong { font-family: 'Montserrat', sans-serif; color: var(--dark-color); }
        .item-row .remove-item-btn { background: none; border: none; color: var(--border-color); font-size: 1.5rem; cursor: pointer; padding: 0 5px; transition: color 0.2s; }
        .item-row .remove-item-btn:hover { color: var(--danger-color); }
        #add-item-btn {
            width: 100%; padding: 12px; background-color: #fff; color: var(--primary-color);
            border: 2px dashed var(--primary-color); border-radius: 6px; cursor: pointer;
            font-weight: 600; font-size: 1rem; margin-top: 10px; transition: all 0.2s;
        }
        #add-item-btn:hover { background-color: var(--primary-color); color: white; }

        /* --- PREVIEW STIJL --- */
        .preview-container { padding: 20px; background-color: #e8e8e8; }
        #quote-preview { transform: scale(0.95); transform-origin: top center; transition: transform 0.3s; }
        
        /* --- OFFERTE SJABLOON ZELF --- */
        .quote-container {
            max-width: 850px; margin: 10px auto; background: #fff; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-radius: 6px; overflow: hidden; font-family: 'Open Sans', sans-serif;
            color: #34495e; font-size: 15px;
            -webkit-print-color-adjust: exact; print-color-adjust: exact;
        }
        .quote-header { display: flex; justify-content: space-between; background-color: #2c3e50; color: #fff; }
        .logo-area { padding: 20px 25px; display: flex; align-items: center; }
        .logo-area img { max-height: 50px; filter: brightness(0) invert(1); }
        .title-area { background-color: #16a085; padding: 20px 30px; clip-path: polygon(20% 0, 100% 0, 100% 100%, 0% 100%); min-width: 250px; text-align: right; }
        .title-area h1 { margin: 0; font-family: 'Montserrat', sans-serif; font-size: 2.2em; }
        .meta-info, .totals-section, .quote-footer { background: #f9f9f9; }
        .meta-info { display: flex; justify-content: space-around; padding: 10px 20px; border-bottom: 1px solid #eee; }
        .meta-block { text-align: center; } .meta-block span { font-size: 0.8em; color: #888; } .meta-block strong { display: block; font-size: 0.9em; color: #2c3e50; }
        .parties-info { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; padding: 20px 25px; border-bottom: 1px solid #ecf0f1; }
        .info-box h3 { margin: 0 0 8px 0; font-family: 'Montserrat', sans-serif; font-size: 0.8em; color: var(--primary-color); border-bottom: 2px solid var(--primary-color); padding-bottom: 4px; letter-spacing: 1px; text-transform: uppercase; }
        .info-box p { margin: 0; line-height: 1.5; font-size: 0.85em; white-space: pre-wrap; }
        .quote-main { padding: 20px 25px; } .quote-main h3 { font-family: 'Montserrat', sans-serif; color: #2c3e50; margin: 0 0 15px 0; font-size: 1.1rem; }
        .item-table { width: 100%; border-collapse: collapse; }
        .item-table thead { background: #2c3e50; color: #fff; }
        .item-table th { padding: 10px 12px; text-align: left; font-weight: 700; font-size: 0.8em; text-transform: uppercase; }
        .item-table tbody tr { border-bottom: 1px solid #ecf0f1; page-break-inside: avoid; }
        .item-table td { padding: 10px 12px; vertical-align: top; } .item-table .description { font-size: 0.8em; color: #777; margin: 4px 0 0 0; white-space: pre-wrap; }
        .item-table th:nth-child(n+2), .item-table td:nth-child(n+2) { text-align: right; }
        .totals-section { display: flex; justify-content: space-between; align-items: flex-start; padding: 20px 25px; background: #ecf0f1; border-bottom: 1px solid #ddd; page-break-inside: avoid; }
        .notes { width: 55%; } .notes h4 { margin: 0 0 5px 0; font-family: 'Montserrat', sans-serif; color: #2c3e50; font-size: 1em; } .notes p { font-size: 0.8em; color: #555; margin: 0; white-space: pre-wrap; }
        .totals-summary { width: 40%; } .totals-table { width: 100%; font-size: 0.9em; } .totals-table td { padding: 2px 0; text-align: right; } .totals-table td:first-child { text-align: left; }
        .grand-total { font-weight: bold; font-size: 1.1em; } .grand-total td { padding-top: 8px; border-top: 2px solid #bdc3c7; }
        .grand-total td:last-child { font-family: 'Montserrat', sans-serif; color: var(--primary-color); font-size: 1.3em; }
        .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px 25px; padding: 20px 25px; border-bottom: 1px solid #ecf0f1; page-break-inside: avoid; }
        .detail-block h4 { font-family: 'Montserrat', sans-serif; color: #2c3e50; margin: 0 0 8px 0; font-size: 0.9em; } .detail-block ul { margin: 0; padding-left: 15px; font-size: 0.85em; line-height: 1.6; } .detail-block p { font-size: 0.85em; line-height: 1.5; margin: 0; white-space: pre-wrap; }
        .detail-block h4:first-line { color: var(--primary-color); }
        .acceptance-section { padding: 20px 25px; page-break-inside: avoid; } .acceptance-section h3 { font-family: 'Montserrat', sans-serif; color: #2c3e50; margin: 0 0 10px 0; font-size: 1.1em; } .acceptance-section p { font-size: 0.85em; margin: 0; }
        .signature-area { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-top: 20px; } .signature-block { border-top: 1px solid #bdc3c7; padding-top: 5px; font-size: 0.8em; color: #555; }
        .quote-footer { padding: 15px 25px; text-align: center; font-size: 0.8em; color: #888; background: #ecf0f1; }
        
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.6); }
        .modal-content { background-color: #fefefe; margin: 10% auto; padding: 30px; border: 1px solid #888; width: 80%; max-width: 600px; border-radius: 8px; box-shadow: 0 5px 20px rgba(0,0,0,0.3); position: relative; }
        .close-btn { color: #aaa; position: absolute; top: 15px; right: 25px; font-size: 28px; font-weight: bold; cursor: pointer; }
        .close-btn:hover, .close-btn:focus { color: black; text-decoration: none; }
        
        @page { size: A4; margin: 10mm; }
        @media print {
            body > *:not(.main-container) { display: none; }
            .main-container > *:not(.preview-container) { display: none; }
            body, .main-container { padding: 0; margin: 0; background: #fff; }
            .preview-container { padding: 0; margin: 0; box-shadow: none; max-height: none; background: #fff; }
            #quote-preview { transform: none; }
            .quote-container { max-width: 100%; margin: 0; box-shadow: none; border: none; font-size: 13.5px; }
            .logo-area img { max-height: 40px; }
            .title-area h1 { font-size: 2em; }
            .logo-area, .title-area, .parties-info, .quote-main, .totals-section, .details-grid, .acceptance-section { padding: 15px 20px; }
            .item-table td, .item-table th { padding: 8px 10px; }
            .info-box p, .detail-block ul, .detail-block p { font-size: 0.9em; }
            .grand-total td:last-child { font-size: 1.2em; }
            .quote-main h3 { margin-bottom: 10px; }
        }
        
        @media (max-width: 992px) { 
            .main-container { grid-template-columns: 1fr; } 
            .form-container, .preview-container { max-height: none; } 
        }
    </style>
</head>
<body>

    <!-- Applicatie Header -->
    <header class="app-header">
        <h1>Offerte Generator</h1>
        <div class="actions">
            <button id="settings-btn">Instellingen</button>
            <button id="generate-pdf">Genereer PDF</button>
        </div>
    </header>

    <!-- Hoofdcontainer met Formulier en Preview -->
    <main class="main-container">
        
        <!-- Linkerkolom: Formulier -->
        <div class="form-container">
            <form id="quote-form" onsubmit="return false;">
                
                <div class="form-section">
                    <h2><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M12,19L8,15H10.5V12H13.5V15H16L12,19Z" /></svg> Offerte Details</h2>
                    <div class="form-grid">
                        <div class="form-group"><label for="offertenummer">Offertenummer</label><input type="text" id="offertenummer" data-target="#preview-offertenummer" value="OFF-2025-001"></div>
                        <div class="form-group"><label for="datum">Datum</label><input type="date" id="datum" data-target="#preview-datum"></div>
                        <div class="form-group"><label for="geldig-tot">Geldig tot</label><input type="date" id="geldig-tot" data-target="#preview-geldig-tot"></div>
                        <div class="form-group full-width"><label for="project-titel">Titel van het project</label><input type="text" id="project-titel" data-target="#preview-project-titel" value="Nieuwe Website & Branding"></div>
                    </div>
                </div>

                <div class="form-section">
                    <h2><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" /></svg> Klantgegevens</h2>
                    <div class="form-grid">
                        <div class="form-group full-width"><label for="ontvanger-naam">Bedrijfsnaam</label><input type="text" id="ontvanger-naam" data-target="#preview-ontvanger-naam" value="[ONTVANGER]"></div>
                        <div class="form-group full-width"><label for="ontvanger-contact">T.a.v. Contactpersoon</label><input type="text" id="ontvanger-contact" data-target="#preview-ontvanger-contact" value="[Contactpersoon]"></div>
                        <div class="form-group full-width"><label for="ontvanger-adres">Adres & Postcode/Stad (één per regel)</label><textarea id="ontvanger-adres" data-target="#preview-ontvanger-adres">[Adres van de klant]
[Postcode en Stad]</textarea></div>
                    </div>
                </div>

                <div class="form-section">
                    <h2><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13.5,8H12V13.5H13.5V8M13.5,15.5H12V17H13.5V15.5M21,3H3A2,2 0 0,0 1,5V19A2,2 0 0,0 3,21H21A2,2 0 0,0 23,19V5A2,2 0 0,0 21,3M21,19H3V5H21V19Z" /></svg> Kostenoverzicht</h2>
                    <div id="items-form-container"></div>
                    <button type="button" id="add-item-btn">+ Regel Toevoegen</button>
                </div>
                
                <div class="form-section">
                    <h2><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.13,5.12L18.88,8.87M3,17.25V21H6.75L17.81,9.94L14.06,6.19L3,17.25Z" /></svg> Aanvullende Informatie</h2>
                    <div class="form-group full-width"><label for="opmerkingen">Opmerkingen</label><textarea id="opmerkingen" data-target="#preview-opmerkingen">Alle genoemde prijzen zijn exclusief 21% BTW. Meerwerk wordt in overleg gefactureerd.</textarea></div>
                    <div class="form-grid">
                        <div class="form-group"><label for="inbegrepen">✓ Inbegrepen (één per regel)</label><textarea id="inbegrepen" data-target-list="#preview-inbegrepen">Responsive design
CMS-installatie
Eén correctieronde</textarea></div>
                        <div class="form-group"><label for="niet-inbegrepen">✗ Niet inbegrepen (één per regel)</label><textarea id="niet-inbegrepen" data-target-list="#preview-niet-inbegrepen">Aankoop stockfotografie
Schrijven van teksten
Kosten externe plug-ins</textarea></div>
                        <div class="form-group"><label for="planning">Indicatieve Planning (één per regel)</label><textarea id="planning" data-target-list="#preview-planning">Fase 1: Design (1-2 wkn)
Fase 2: Bouw (3-4 wkn)
Fase 3: Oplevering (1 wk)</textarea></div>
                        <div class="form-group"><label for="voorwaarden">Voorwaarden</label><textarea id="voorwaarden" data-target="#preview-voorwaarden">Betaling: 50% bij aanvang, 50% bij oplevering. Termijn: 14 dagen. Algemene voorwaarden van toepassing.</textarea></div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Rechterkolom: Offerte Preview -->
        <div class="preview-container">
            <div id="quote-preview">
                <div class="quote-container">
                    <header class="quote-header">
                        <div class="logo-area"><img src="https://via.placeholder.com/150x50.png?text=Logo" alt="Bedrijfslogo" id="preview-logo"></div>
                        <div class="title-area"><h1>OFFERTE</h1></div>
                    </header>
                    <section class="meta-info">
                        <div class="meta-block"><span>Offertenummer</span><strong id="preview-offertenummer"></strong></div>
                        <div class="meta-block"><span>Datum</span><strong id="preview-datum"></strong></div>
                        <div class="meta-block"><span>Geldig tot</span><strong id="preview-geldig-tot"></strong></div>
                    </section>
                    <section class="parties-info">
                        <div class="info-box"><h3>VERZENDER</h3><p id="preview-verzender-info"></p></div>
                        <div class="info-box"><h3>ONTVANGER</h3><p><strong id="preview-ontvanger-naam"></strong><br>T.a.v. <span id="preview-ontvanger-contact"></span><br><span id="preview-ontvanger-adres"></span></p></div>
                    </section>
                    <main class="quote-main">
                        <h3>Kostenoverzicht: <span id="preview-project-titel"></span></h3>
                        <table class="item-table">
                            <thead><tr><th>Omschrijving</th><th>Aantal</th><th>Prijs p/s</th><th>Totaal</th></tr></thead>
                            <tbody id="preview-items-body"></tbody>
                        </table>
                    </main>
                    <section class="totals-section">
                        <div class="notes"><h4>Opmerkingen</h4><p id="preview-opmerkingen"></p></div>
                        <div class="totals-summary">
                            <table class="totals-table">
                                <tr><td>Subtotaal</td><td id="preview-subtotaal">€ 0,00</td></tr>
                                <tr><td>BTW (21%)</td><td id="preview-btw">€ 0,00</td></tr>
                                <tr class="grand-total"><td>TOTAAL</td><td id="preview-totaal">€ 0,00</td></tr>
                            </table>
                        </div>
                    </section>
                    <section class="details-grid">
                        <div class="detail-block"><h4>✓ Inbegrepen</h4><ul id="preview-inbegrepen"></ul></div>
                        <div class="detail-block"><h4>✗ Niet inbegrepen</h4><ul id="preview-niet-inbegrepen"></ul></div>
                        <div class="detail-block"><h4>Indicatieve Planning</h4><ul id="preview-planning"></ul></div>
                        <div class="detail-block"><h4>Voorwaarden</h4><p id="preview-voorwaarden"></p></div>
                    </section>
                    <section class="acceptance-section">
                        <h3>Akkoordverklaring</h3>
                        <p>Voor akkoord, graag een ondertekende kopie van deze offerte retourneren per e-mail.</p>
                        <div class="signature-area">
                            <div class="signature-block"><span>Naam</span></div>
                            <div class="signature-block"><span>Datum</span></div>
                            <div class="signature-block"><span>Handtekening</span></div>
                        </div>
                    </section>
                    <footer class="quote-footer">
                        <p><strong id="preview-footer-bedrijfsnaam"></strong> | <span id="preview-footer-website"></span></p>
                    </footer>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal voor Instellingen -->
    <div id="settings-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn">×</span>
            <h2>Instellingen Bedrijfsgegevens</h2>
            <p>Deze gegevens worden opgeslagen in je browser voor toekomstig gebruik.</p>
            <form id="settings-form" onsubmit="return false;">
                <div class="form-grid">
                    <div class="form-group full-width"><label for="setting-bedrijfsnaam">Bedrijfsnaam</label><input type="text" id="setting-bedrijfsnaam"></div>
                    <div class="form-group full-width"><label for="setting-adres">Adres</label><input type="text" id="setting-adres" placeholder="Straatnaam Huisnr, Postcode Stad"></div>
                    <div class="form-group"><label for="setting-email">E-mailadres</label><input type="email" id="setting-email"></div>
                    <div class="form-group"><label for="setting-kvk">KvK-nummer</label><input type="text" id="setting-kvk"></div>
                    <div class="form-group"><label for="setting-website">Website</label><input type="text" id="setting-website" placeholder="jouwwebsite.nl"></div>
                    <div class="form-group"><label for="setting-logo">Logo URL</label><input type="text" id="setting-logo" placeholder="https://.../logo.png"></div>
                </div>
                <button type="submit" id="save-settings-btn" style="background: var(--primary-color); color: white; border: none; padding: 12px 25px; border-radius: 5px; cursor: pointer; font-weight: 600; width: 100%; font-size: 1rem; margin-top: 20px;">Opslaan</button>
            </form>
        </div>
    </div>

    <script>
        // Wacht tot de volledige HTML-pagina is geladen voordat het script wordt uitgevoerd.
        document.addEventListener('DOMContentLoaded', () => {

            // --- HULPFUNCTIES ---
            const formatCurrency = value => new Intl.NumberFormat('nl-NL', { style: 'currency', currency: 'EUR' }).format(value);
            const formatDate = dateString => {
                if (!dateString) return '';
                const [year, month, day] = dateString.split('-');
                return `${day}-${month}-${year}`;
            };

            // --- INSTELLINGEN (LOCALSTORAGE) ---
            const settingsModal = document.getElementById('settings-modal');
            const defaultSettings = {
                bedrijfsnaam: 'A.S.Allround klussen',
                adres: 'Kelspelstraat 3, 3641 JS Mijdrecht',
                email: '<EMAIL>',
                kvk: '[Jouw KvK-nr]',
                website: 'jouwwebsite.nl',
                logo: 'https://via.placeholder.com/150x50.png?text=Logo'
            };

            const saveSettings = () => {
                const settings = {
                    bedrijfsnaam: document.getElementById('setting-bedrijfsnaam').value,
                    adres: document.getElementById('setting-adres').value,
                    email: document.getElementById('setting-email').value,
                    kvk: document.getElementById('setting-kvk').value,
                    website: document.getElementById('setting-website').value,
                    logo: document.getElementById('setting-logo').value
                };
                localStorage.setItem('quoteGeneratorSettings', JSON.stringify(settings));
                loadSettings(); // Herlaad instellingen om de preview bij te werken
                settingsModal.style.display = 'none';
            };

            const loadSettings = () => {
                const savedSettings = JSON.parse(localStorage.getItem('quoteGeneratorSettings')) || defaultSettings;
                Object.keys(savedSettings).forEach(key => {
                    const el = document.getElementById(`setting-${key.replace('_', '-')}`);
                    if (el) el.value = savedSettings[key];
                });
                updateCompanyInfo(savedSettings);
            };

            const updateCompanyInfo = (settings) => {
                document.getElementById('preview-verzender-info').innerHTML = `<strong>${settings.bedrijfsnaam}</strong><br>${settings.adres}<br><strong>E:</strong> ${settings.email} | <strong>KVK:</strong> ${settings.kvk}`;
                document.getElementById('preview-footer-bedrijfsnaam').textContent = settings.bedrijfsnaam;
                document.getElementById('preview-footer-website').textContent = settings.website;
                document.getElementById('preview-logo').src = settings.logo || defaultSettings.logo;
            };

            // Event listeners voor de instellingen-modal
            document.getElementById('settings-btn').addEventListener('click', () => settingsModal.style.display = 'block');
            document.querySelector('.close-btn').addEventListener('click', () => settingsModal.style.display = 'none');
            window.addEventListener('click', (e) => {
                if (e.target == settingsModal) settingsModal.style.display = 'none';
            });
            document.getElementById('settings-form').addEventListener('submit', (e) => {
                e.preventDefault();
                saveSettings();
            });
            
            // --- BEHEER VAN OFFERTEREGELS ---
            const itemsFormContainer = document.getElementById('items-form-container');
            const itemsPreviewBody = document.getElementById('preview-items-body');
            let itemId = 0;

            const updateTotals = () => {
                let subtotal = 0;
                itemsPreviewBody.innerHTML = ''; // Leeg de preview tabel
                
                itemsFormContainer.querySelectorAll('.item-row').forEach(row => {
                    const description = row.querySelector('.item-description').value;
                    const longDescription = row.querySelector('.item-long-description').value;
                    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
                    const price = parseFloat(row.querySelector('.item-price').value) || 0;
                    const total = quantity * price;
                    subtotal += total;

                    // Voeg de regel toe aan de preview tabel
                    const previewRow = document.createElement('tr');
                    previewRow.innerHTML = `<td><strong>${description || '...'}</strong><p class="description">${longDescription.replace(/\n/g, '<br>')}</p></td><td>${quantity}</td><td>${formatCurrency(price)}</td><td>${formatCurrency(total)}</td>`;
                    itemsPreviewBody.appendChild(previewRow);
                });

                // Bereken en update totalen
                const btw = subtotal * 0.21;
                const total = subtotal + btw;
                document.getElementById('preview-subtotaal').textContent = formatCurrency(subtotal);
                document.getElementById('preview-btw').textContent = formatCurrency(btw);
                document.getElementById('preview-totaal').textContent = formatCurrency(total);
            };

            const addItemRow = (desc = '', longDesc = '', qty = 1, price = 0) => {
                itemId++;
                const row = document.createElement('div');
                row.className = 'item-row';
                row.innerHTML = `
                    <div class="item-row-header">
                        <strong>Regel #${itemId}</strong>
                        <button type="button" class="remove-item-btn" title="Verwijder regel">×</button>
                    </div>
                    <div class="form-group full-width">
                        <label for="item-desc-${itemId}">Korte Omschrijving</label>
                        <input type="text" class="item-description" id="item-desc-${itemId}" value="${desc}">
                    </div>
                    <div class="form-group full-width">
                        <label for="item-long-desc-${itemId}">Lange Omschrijving</label>
                        <textarea class="item-long-description" id="item-long-desc-${itemId}">${longDesc}</textarea>
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="item-qty-${itemId}">Aantal</label>
                            <input type="number" class="item-quantity" id="item-qty-${itemId}" value="${qty}" step="any">
                        </div>
                        <div class="form-group">
                            <label for="item-price-${itemId}">Prijs p/s</label>
                            <input type="number" class="item-price" id="item-price-${itemId}" value="${price}" step="0.01">
                        </div>
                    </div>`;
                itemsFormContainer.appendChild(row);

                // Voeg een event listener toe aan de verwijderknop
                row.querySelector('.remove-item-btn').addEventListener('click', () => {
                    row.remove();
                    updateTotals(); // Werk de totalen bij na verwijderen
                });
            };

            document.getElementById('add-item-btn').addEventListener('click', () => addItemRow());

            // --- SYNCHRONISATIE TUSSEN FORMULIER EN PREVIEW ---
            const syncInput = (e) => {
                const el = e.target;
                const targetSelector = el.dataset.target;

                if (targetSelector) { // Voor normale velden
                    const targetElement = document.querySelector(targetSelector);
                    if (!targetElement) return;

                    if (el.type === 'date') {
                        targetElement.textContent = formatDate(el.value);
                    } else if (el.tagName === 'TEXTAREA') {
                        targetElement.innerHTML = el.value.replace(/\n/g, '<br>');
                    } else {
                        targetElement.textContent = el.value;
                    }
                } else if (el.dataset.targetList) { // Voor velden die een lijst moeten worden
                    const targetListElement = document.querySelector(el.dataset.targetList);
                    if (!targetListElement) return;

                    const items = el.value.split('\n').filter(item => item.trim() !== '');
                    targetListElement.innerHTML = items.map(item => `<li>${item}</li>`).join('');
                } else if (el.closest('.item-row')) { // Als een veld in een offerteregel wordt aangepast
                    updateTotals();
                }
            };

            document.getElementById('quote-form').addEventListener('input', syncInput);
            
            // --- PDF GENERATIE ---
            document.getElementById('generate-pdf').addEventListener('click', () => {
                window.print();
            });


            // --- INITIALISATIE ---
            const init = () => {
                // Stel standaard datums in
                const today = new Date();
                const validUntil = new Date();
                validUntil.setDate(today.getDate() + 30);
                document.getElementById('datum').value = today.toISOString().split('T')[0];
                document.getElementById('geldig-tot').value = validUntil.toISOString().split('T')[0];

                // Laad opgeslagen bedrijfsinstellingen
                loadSettings();

                // Voeg standaard offerteregels toe voor demonstratie
                addItemRow('Strategie & Conceptontwikkeling', 'Uitwerken van visuele identiteit en projectstrategie.', 1, 850);
                addItemRow('Webdesign & Realisatie', 'Ontwerp en bouw van een maatwerk WordPress website.', 1, 3200);
                addItemRow('Service & Hosting (1 jaar)', 'Hosting, onderhoud, updates en support.', 1, 450);

                // Roep voor alle velden de 'input' event aan om de preview te vullen
                document.querySelectorAll('#quote-form input, #quote-form textarea').forEach(el => {
                    el.dispatchEvent(new Event('input', { bubbles: true }));
                });
                
                // Werk de totalen de eerste keer bij
                updateTotals();
            };

            // Start de applicatie
            init();
        });
    </script>
</body>
</html>